import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';

interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  provider?: string;
  responseTime?: number;
}

interface ConnectivityResult {
  openrouter: { success: boolean; details: any };
  openai: { success: boolean; details: any };
}

export default function AITestPage() {
  const { data: session, status } = useSession();
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [userRole, setUserRole] = useState<'EXPERT' | 'CLIENT'>('EXPERT');
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const [connectivity, setConnectivity] = useState<ConnectivityResult | null>(null);
  const [isTestingConnectivity, setIsTestingConnectivity] = useState(false);

  // Test AI connectivity on component mount
  useEffect(() => {
    testConnectivity();
  }, []);

  const testConnectivity = async () => {
    setIsTestingConnectivity(true);
    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'connectivity' })
      });
      
      if (response.ok) {
        const data = await response.json();
        setConnectivity(data.data);
      }
    } catch (error) {
      console.error('Connectivity test failed:', error);
    } finally {
      setIsTestingConnectivity(false);
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isSending) return;

    const userMessage: AIMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsSending(true);

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue.trim(),
          userRole,
          language,
          conversationContext: {
            messageCount: messages.length,
            userRole,
            language
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Request failed: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        const aiMessage: AIMessage = {
          id: data.data.aiMessage.id,
          role: 'assistant',
          content: data.data.aiMessage.content,
          timestamp: data.data.aiMessage.timestamp,
          provider: data.data.aiMessage.provider,
          responseTime: data.data.aiMessage.responseTime
        };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        throw new Error(data.error || 'Failed to get AI response');
      }
    } catch (error: any) {
      console.error('Failed to send message:', error);
      const errorMessage: AIMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: `❌ خطأ: ${error.message}`,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">جاري التحميل...</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <h1 className="text-2xl mb-4">يرجى تسجيل الدخول أولاً</h1>
          <button 
            onClick={() => window.location.href = '/'}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg"
          >
            العودة للصفحة الرئيسية
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo" dir="rtl">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            🧪 اختبار الذكاء الاصطناعي - فريلا سوريا
          </h1>
          <p className="text-gray-300">
            اختبار النظام الذكي مع نظام الاحتياط التلقائي
          </p>
          <p className="text-sm text-gray-400 mt-2">
            مرحباً {session.user?.name} ({session.user?.email})
          </p>
        </div>

        {/* Connectivity Status */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">حالة الاتصال</h2>
            <button
              onClick={testConnectivity}
              disabled={isTestingConnectivity}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-4 py-2 rounded-lg text-white text-sm"
            >
              {isTestingConnectivity ? 'جاري الاختبار...' : 'اختبار الاتصال'}
            </button>
          </div>
          
          {connectivity && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className={`p-4 rounded-lg ${connectivity.openrouter.success ? 'bg-green-900/30 border-green-500' : 'bg-red-900/30 border-red-500'} border`}>
                <h3 className="font-semibold text-white mb-2">OpenRouter</h3>
                <p className={`text-sm ${connectivity.openrouter.success ? 'text-green-400' : 'text-red-400'}`}>
                  {connectivity.openrouter.success ? '✅ متصل' : '❌ غير متصل'}
                </p>
                {connectivity.openrouter.details.error && (
                  <p className="text-xs text-red-300 mt-1">{connectivity.openrouter.details.error}</p>
                )}
              </div>
              
              <div className={`p-4 rounded-lg ${connectivity.openai.success ? 'bg-green-900/30 border-green-500' : 'bg-red-900/30 border-red-500'} border`}>
                <h3 className="font-semibold text-white mb-2">OpenAI</h3>
                <p className={`text-sm ${connectivity.openai.success ? 'text-green-400' : 'text-red-400'}`}>
                  {connectivity.openai.success ? '✅ متصل' : '❌ غير متصل'}
                </p>
                {connectivity.openai.details.error && (
                  <p className="text-xs text-red-300 mt-1">{connectivity.openai.details.error}</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white mb-2">نوع المستخدم</label>
              <select
                value={userRole}
                onChange={(e) => setUserRole(e.target.value as 'EXPERT' | 'CLIENT')}
                className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 border border-gray-600"
              >
                <option value="EXPERT">خبير</option>
                <option value="CLIENT">عميل</option>
              </select>
            </div>
            
            <div>
              <label className="block text-white mb-2">اللغة</label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value as 'ar' | 'en')}
                className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 border border-gray-600"
              >
                <option value="ar">العربية</option>
                <option value="en">English</option>
              </select>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700 min-h-[400px] max-h-[600px] overflow-y-auto">
          {messages.length === 0 ? (
            <div className="text-center text-gray-400 py-8">
              <p>ابدأ محادثة جديدة مع الذكاء الاصطناعي</p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[80%] p-4 rounded-xl ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-white'
                  }`}>
                    <p className="whitespace-pre-wrap">{message.content}</p>
                    <div className="text-xs opacity-70 mt-2 flex items-center gap-2">
                      <span>{new Date(message.timestamp).toLocaleTimeString('ar-SY')}</span>
                      {message.provider && (
                        <span className="bg-gray-600 px-2 py-1 rounded">
                          {message.provider}
                        </span>
                      )}
                      {message.responseTime && (
                        <span className="bg-gray-600 px-2 py-1 rounded">
                          {message.responseTime}ms
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Input */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
          <div className="flex gap-4">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالتك هنا..."
              className="flex-1 bg-gray-700 text-white rounded-lg px-4 py-3 border border-gray-600 resize-none"
              rows={3}
              disabled={isSending}
            />
            <button
              onClick={sendMessage}
              disabled={!inputValue.trim() || isSending}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-6 py-3 rounded-lg text-white font-semibold"
            >
              {isSending ? 'جاري الإرسال...' : 'إرسال'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
