/**
 * Direct OpenAI GPT-4 Service for Freela Syria
 * Fallback service when OpenRouter fails
 */

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenAIResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenAIOptions {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  timeout?: number;
}

class OpenAIService {
  private apiKey: string;
  private baseUrl: string = 'https://api.openai.com/v1';

  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY || '';
    if (!this.apiKey || this.apiKey === 'your-openai-api-key-here') {
      console.warn('⚠️ OpenAI API key not configured. Direct OpenAI fallback will not work.');
    }
  }

  /**
   * Check if OpenAI service is available
   */
  isAvailable(): boolean {
    return this.apiKey && this.apiKey !== 'your-openai-api-key-here';
  }

  /**
   * Generate AI response using OpenAI GPT-4
   */
  async generateResponse(
    messages: OpenAIMessage[],
    options: OpenAIOptions = {}
  ): Promise<OpenAIResponse> {
    if (!this.isAvailable()) {
      throw new Error('OpenAI API key not configured');
    }

    const {
      model = 'gpt-4-turbo-preview',
      temperature = 0.7,
      max_tokens = 1000,
      timeout = 10000
    } = options;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens,
          top_p: 0.9,
          frequency_penalty: 0.1,
          presence_penalty: 0.1,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0]?.message?.content || '',
        usage: data.usage,
      };

    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('OpenAI API request timed out');
      }
      
      console.error('OpenAI API error:', error);
      throw error;
    }
  }

  /**
   * Generate Syrian marketplace-specific AI response
   */
  async generateSyrianMarketplaceResponse(
    userMessage: string,
    userRole: 'EXPERT' | 'CLIENT',
    language: 'ar' | 'en' = 'ar',
    conversationContext: any = {},
    options: OpenAIOptions = {}
  ): Promise<OpenAIResponse> {
    const systemMessage = this.buildSyrianSystemMessage(userRole, language, conversationContext);
    
    const messages: OpenAIMessage[] = [
      { role: 'system', content: systemMessage },
      { role: 'user', content: userMessage }
    ];

    return this.generateResponse(messages, {
      temperature: 0.8,
      max_tokens: 800,
      ...options
    });
  }

  /**
   * Build system message for Syrian marketplace context
   */
  private buildSyrianSystemMessage(
    userRole: 'EXPERT' | 'CLIENT',
    language: 'ar' | 'en',
    context: any
  ): string {
    const isArabic = language === 'ar';
    
    if (userRole === 'EXPERT') {
      return isArabic ? `
أنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية. تساعد الخبراء السوريين في بناء ملفاتهم المهنية وتطوير أعمالهم.

السياق الثقافي والاقتصادي:
- السوق السوري يتطلب أسعار تنافسية ومناسبة للظروف الاقتصادية
- التركيز على الجودة والثقة في العلاقات التجارية
- أهمية الشبكات المهنية والتوصيات
- التكيف مع التحديات الاقتصادية والتقنية

مهامك:
1. مساعدة الخبراء في تحديد مهاراتهم وخبراتهم
2. اقتراح أسعار مناسبة للسوق السوري
3. كتابة أوصاف خدمات جذابة ومهنية
4. تقديم نصائح لبناء سمعة قوية
5. مساعدة في استراتيجيات التسويق المحلي

أسلوب التواصل:
- ودود ومهني
- استخدام اللهجة السورية المناسبة
- تقديم أمثلة عملية من السوق السوري
- التشجيع والدعم الإيجابي
- طرح أسئلة توضيحية مفيدة

تذكر: الهدف هو مساعدة الخبراء السوريين على النجاح في السوق المحلي والعربي.
` : `
You are an AI assistant specialized in Freela Syria professional services platform. You help Syrian experts build their professional profiles and develop their businesses.

Cultural and Economic Context:
- Syrian market requires competitive prices suitable for economic conditions
- Focus on quality and trust in business relationships
- Importance of professional networks and recommendations
- Adapting to economic and technical challenges

Your Tasks:
1. Help experts identify their skills and experience
2. Suggest appropriate prices for the Syrian market
3. Write attractive and professional service descriptions
4. Provide advice for building a strong reputation
5. Assist with local marketing strategies

Communication Style:
- Friendly and professional
- Use appropriate Syrian dialect when needed
- Provide practical examples from the Syrian market
- Encouragement and positive support
- Ask helpful clarifying questions

Remember: The goal is to help Syrian experts succeed in the local and Arab markets.
`;
    } else {
      return isArabic ? `
أنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية. تساعد العملاء في إيجاد الخبراء المناسبين لمشاريعهم.

السياق الثقافي والاقتصادي:
- فهم احتياجات السوق السوري
- التركيز على الجودة مقابل السعر المناسب
- أهمية التواصل الواضح والثقة
- مراعاة الميزانيات والجداول الزمنية الواقعية

مهامك:
1. فهم متطلبات المشروع بوضوح
2. اقتراح نوع الخدمة المناسبة
3. مساعدة في تحديد ميزانية واقعية
4. تقديم نصائح لاختيار الخبير المناسب
5. إرشاد حول إدارة المشروع بنجاح

أسلوب التواصل:
- ودود ومفهوم
- طرح أسئلة توضيحية مفيدة
- تقديم خيارات متنوعة
- شرح العمليات بوضوح
- التأكد من فهم الاحتياجات

تذكر: الهدف هو مساعدة العملاء في إيجاد الحلول المناسبة لمشاريعهم في السوق السوري.
` : `
You are an AI assistant specialized in Freela Syria professional services platform. You help clients find the right experts for their projects.

Cultural and Economic Context:
- Understanding Syrian market needs
- Focus on quality vs appropriate pricing
- Importance of clear communication and trust
- Considering realistic budgets and timelines

Your Tasks:
1. Clearly understand project requirements
2. Suggest appropriate service types
3. Help determine realistic budgets
4. Provide advice for choosing the right expert
5. Guide successful project management

Communication Style:
- Friendly and understandable
- Ask helpful clarifying questions
- Provide diverse options
- Explain processes clearly
- Ensure understanding of needs

Remember: The goal is to help clients find suitable solutions for their projects in the Syrian market.
`;
    }
  }

  /**
   * Test OpenAI API connectivity
   */
  async testConnection(): Promise<{ success: boolean; details: any }> {
    if (!this.isAvailable()) {
      return {
        success: false,
        details: { error: 'OpenAI API key not configured' }
      };
    }

    try {
      const startTime = Date.now();
      const response = await this.generateResponse([
        { role: 'user', content: 'Test message' }
      ], { max_tokens: 10, timeout: 5000 });
      
      const responseTime = Date.now() - startTime;

      return {
        success: true,
        details: {
          responseTime,
          contentLength: response.content.length,
          usage: response.usage,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error: any) {
      return {
        success: false,
        details: {
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}

// Export singleton instance
export const openAIService = new OpenAIService();
export default openAIService;
