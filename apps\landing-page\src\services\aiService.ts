/**
 * Intelligent AI Service with Fallback System
 * Tries OpenRouter first, then falls back to OpenAI GPT-4
 */

import { openAIService } from './openaiService';

interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIResponse {
  content: string;
  provider: 'openrouter' | 'openai' | 'fallback';
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
  responseTime: number;
}

interface AIOptions {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  timeout?: number;
  skipOpenRouter?: boolean;
}

class AIService {
  private openRouterApiKey: string;
  private openRouterBaseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.openRouterApiKey = process.env.OPENROUTER_API_KEY || '';
  }

  /**
   * Generate AI response with intelligent fallback
   */
  async generateResponse(
    messages: AIMessage[],
    userRole: 'EXPERT' | 'CLIENT',
    language: 'ar' | 'en' = 'ar',
    conversationContext: any = {},
    options: AIOptions = {}
  ): Promise<AIResponse> {
    const startTime = Date.now();
    
    // Try OpenRouter first (unless explicitly skipped)
    if (!options.skipOpenRouter && this.openRouterApiKey) {
      try {
        console.log('🔄 Trying OpenRouter API...');
        const openRouterResponse = await this.tryOpenRouter(messages, options);
        
        return {
          content: openRouterResponse.content,
          provider: 'openrouter',
          usage: openRouterResponse.usage,
          responseTime: Date.now() - startTime
        };
      } catch (error: any) {
        console.warn('⚠️ OpenRouter failed, trying OpenAI fallback:', error.message);
      }
    }

    // Try OpenAI as fallback
    if (openAIService.isAvailable()) {
      try {
        console.log('🔄 Trying OpenAI API...');
        const userMessage = messages.find(m => m.role === 'user')?.content || '';
        const openAIResponse = await openAIService.generateSyrianMarketplaceResponse(
          userMessage,
          userRole,
          language,
          conversationContext,
          options
        );
        
        return {
          content: openAIResponse.content,
          provider: 'openai',
          usage: openAIResponse.usage,
          responseTime: Date.now() - startTime
        };
      } catch (error: any) {
        console.warn('⚠️ OpenAI also failed:', error.message);
      }
    }

    // Final fallback to static responses
    console.log('🔄 Using static fallback response...');
    return {
      content: this.generateStaticFallback(userRole, language),
      provider: 'fallback',
      responseTime: Date.now() - startTime
    };
  }

  /**
   * Try OpenRouter API
   */
  private async tryOpenRouter(
    messages: AIMessage[],
    options: AIOptions
  ): Promise<{ content: string; usage?: any }> {
    const {
      model = 'openai/gpt-4-turbo-preview',
      temperature = 0.7,
      max_tokens = 1000,
      timeout = 10000
    } = options;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(`${this.openRouterBaseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://freela-syria.com',
          'X-Title': 'Freela Syria AI Chat',
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens,
          top_p: 0.9,
          frequency_penalty: 0.1,
          presence_penalty: 0.1,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0]?.message?.content || '',
        usage: data.usage,
      };

    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('OpenRouter API request timed out');
      }
      
      throw error;
    }
  }

  /**
   * Generate static fallback response
   */
  private generateStaticFallback(
    userRole: 'EXPERT' | 'CLIENT',
    language: 'ar' | 'en'
  ): string {
    if (language === 'ar') {
      if (userRole === 'EXPERT') {
        return `🇸🇾 أهلاً بك في فريلا سوريا!

عذراً، نواجه مشكلة مؤقتة في خدمة الذكاء الاصطناعي. 

**يمكنك المتابعة بالطرق التالية:**
✅ أخبرني عن مهاراتك وخبراتك
✅ اذكر نوع الخدمات التي تقدمها
✅ حدد أسعارك المفضلة
✅ اكتب وصف مختصر عن نفسك

سأساعدك في إعداد ملفك الشخصي بمجرد عودة الخدمة للعمل.

*يرجى المحاولة مرة أخرى خلال دقائق قليلة* 🔄`;
      } else {
        return `🇸🇾 أهلاً بك في فريلا سوريا!

عذراً، نواجه مشكلة مؤقتة في خدمة الذكاء الاصطناعي.

**يمكنك المتابعة بالطرق التالية:**
✅ أخبرني عن مشروعك المطلوب
✅ حدد نوع الخدمة التي تحتاجها
✅ اذكر ميزانيتك المتوقعة
✅ حدد الجدول الزمني المطلوب

سأساعدك في إيجاد الخبير المناسب بمجرد عودة الخدمة للعمل.

*يرجى المحاولة مرة أخرى خلال دقائق قليلة* 🔄`;
      }
    } else {
      if (userRole === 'EXPERT') {
        return `🇸🇾 Welcome to Freela Syria!

Sorry, we're experiencing a temporary issue with our AI service.

**You can continue with the following:**
✅ Tell me about your skills and experience
✅ Mention the type of services you provide
✅ Set your preferred prices
✅ Write a brief description about yourself

I'll help you set up your profile once the service is back online.

*Please try again in a few minutes* 🔄`;
      } else {
        return `🇸🇾 Welcome to Freela Syria!

Sorry, we're experiencing a temporary issue with our AI service.

**You can continue with the following:**
✅ Tell me about your required project
✅ Specify the type of service you need
✅ Mention your expected budget
✅ Set the required timeline

I'll help you find the right expert once the service is back online.

*Please try again in a few minutes* 🔄`;
      }
    }
  }

  /**
   * Test AI service connectivity
   */
  async testConnectivity(): Promise<{
    openrouter: { success: boolean; details: any };
    openai: { success: boolean; details: any };
  }> {
    const results = {
      openrouter: { success: false, details: {} },
      openai: { success: false, details: {} }
    };

    // Test OpenRouter
    if (this.openRouterApiKey) {
      try {
        const startTime = Date.now();
        await this.tryOpenRouter([
          { role: 'user', content: 'Test message' }
        ], { max_tokens: 10, timeout: 5000 });
        
        results.openrouter = {
          success: true,
          details: {
            responseTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
          }
        };
      } catch (error: any) {
        results.openrouter = {
          success: false,
          details: { error: error.message }
        };
      }
    }

    // Test OpenAI
    results.openai = await openAIService.testConnection();

    return results;
  }

  /**
   * Build system message for conversation context
   */
  buildSystemMessage(
    userRole: 'EXPERT' | 'CLIENT',
    language: 'ar' | 'en',
    conversationContext: any = {}
  ): AIMessage {
    const isArabic = language === 'ar';
    
    const basePrompt = isArabic 
      ? `أنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية.`
      : `You are an AI assistant specialized in Freela Syria professional services platform.`;

    const roleSpecificPrompt = userRole === 'EXPERT'
      ? (isArabic 
          ? `تساعد الخبراء السوريين في بناء ملفاتهم المهنية وتطوير أعمالهم في السوق السوري والعربي.`
          : `You help Syrian experts build their professional profiles and develop their businesses in the Syrian and Arab markets.`)
      : (isArabic
          ? `تساعد العملاء في إيجاد الخبراء المناسبين لمشاريعهم في السوق السوري.`
          : `You help clients find the right experts for their projects in the Syrian market.`);

    const culturalContext = isArabic
      ? `\n\nالسياق الثقافي: مراعاة الظروف الاقتصادية السورية، التركيز على الجودة والثقة، استخدام اللهجة المناسبة.`
      : `\n\nCultural Context: Consider Syrian economic conditions, focus on quality and trust, use appropriate communication style.`;

    return {
      role: 'system',
      content: basePrompt + ' ' + roleSpecificPrompt + culturalContext
    };
  }
}

// Export singleton instance
export const aiService = new AIService();
export default aiService;
