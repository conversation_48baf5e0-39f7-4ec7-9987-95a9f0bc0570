# 🗄️ Expert Profile Database Design
## Freela Syria - AI-Generated Profile Data Structure

> **Document Version**: 1.0  
> **Last Updated**: 2025-01-23  
> **Database**: Supabase PostgreSQL  
> **ORM**: Prisma  
> **Status**: Planning Phase  

---

## 🎯 **DESIGN OBJECTIVES**

### **Primary Goals**
1. **Seamless Integration**: Work with existing expert dashboard structure
2. **AI Compatibility**: Support AI-generated profile data
3. **Arabic RTL Support**: Full localization for Syrian market
4. **Scalability**: Handle growing expert base efficiently
5. **Data Quality**: Ensure profile completeness and accuracy

---

## 📊 **CURRENT EXPERT PROFILE ANALYSIS**

### **Existing Structure (From Dashboard)**
```typescript
interface ExpertProfile {
  id: string;
  name: string;
  title: string;
  bio: string;
  avatar?: string;
  email: string;
  phone: string;
  location: string;
  languages: string[];
  skills: string[];
  experience: number;
  education: string;
  certifications: string[];
  hourlyRate: number;
  availability: string;
  verified: boolean;
  rating: number;
  completedProjects: number;
  joinDate: string;
}
```

### **Database Schema (Current)**
```typescript
model ExpertProfile {
  id                    String    @id @default(cuid())
  userId                String    @unique
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  title                 Json      // LocalizedString
  description           Json      // LocalizedString
  skills                String[]
  experience            String    // ExperienceLevel
  hourlyRate            Float?
  availability          Json      // Availability object
  responseTime          String    // ResponseTime
  completedProjects     Int       @default(0)
  rating                Float     @default(0)
  reviewCount           Int       @default(0)
  verified              Boolean   @default(false)
  verificationDocuments Json[]    // FileUpload[]
  
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}
```

---

## 🆕 **ENHANCED EXPERT PROFILE STRUCTURE**

### **AI-Enhanced Expert Profile Model**
```typescript
model ExpertProfile {
  id                    String    @id @default(cuid())
  userId                String    @unique
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Basic Professional Information
  title                 Json      // LocalizedString - AI generated professional title
  description           Json      // LocalizedString - AI crafted compelling description
  tagline               Json?     // LocalizedString - Short catchy tagline
  specializations       String[]  // Detailed specialization areas
  
  // Skills & Expertise (AI Enhanced)
  skills                String[]  // Core technical skills
  softSkills            String[]  // Communication, leadership, etc.
  tools                 String[]  // Software, platforms, frameworks
  skillLevels           Json      // Skill proficiency mapping
  
  // Experience & Background
  experience            String    // ExperienceLevel: BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
  experienceYears       Int?      // Total years of experience
  industryExperience    Json[]    // Array of industry experience objects
  previousRoles         Json[]    // Array of previous job roles
  
  // Education & Certifications (AI Structured)
  education             Json[]    // Array of education objects
  certifications        Json[]    // Array of certification objects
  achievements          Json[]    // Professional achievements and awards
  
  // Service Offerings (AI Generated)
  serviceCategories     String[]  // Main service categories
  serviceOfferings      Json[]    // Detailed service packages
  pricingStrategy       Json      // Pricing approach and rationale
  
  // Pricing & Availability
  hourlyRate            Float?
  hourlyRateMin         Float?    // Minimum hourly rate
  hourlyRateMax         Float?    // Maximum hourly rate
  availability          Json      // Availability object with schedule
  responseTime          String    // Expected response time
  workingHours          Json      // Detailed working hours
  
  // Portfolio & Showcase (AI Enhanced)
  portfolioItems        Json[]    // Array of portfolio objects
  testimonials          Json[]    // Client testimonials
  caseStudies           Json[]    // Detailed project case studies
  
  // Performance Metrics
  completedProjects     Int       @default(0)
  rating                Float     @default(0)
  reviewCount           Int       @default(0)
  successRate           Float     @default(0)
  onTimeDelivery        Float     @default(0)
  
  // Verification & Trust
  verified              Boolean   @default(false)
  verificationLevel     String    @default("BASIC") // BASIC, VERIFIED, PREMIUM
  verificationDocuments Json[]    // FileUpload[]
  trustScore            Float     @default(0)
  
  // AI Integration Fields
  aiGenerated           Boolean   @default(false)
  aiSessionId           String?   // Reference to AI conversation session
  aiConfidenceScore     Float?    // AI confidence in generated profile
  aiLastOptimized       DateTime?
  
  // Profile Quality & Completeness
  profileCompleteness   Float     @default(0)
  profileQualityScore   Float     @default(0)
  profileViews          Int       @default(0)
  profileClicks         Int       @default(0)
  conversionRate        Float     @default(0)
  
  // Business Information
  businessType          String?   // INDIVIDUAL, FREELANCER, AGENCY
  teamSize              Int?      // For agencies
  companyInfo           Json?     // Company details if applicable
  
  // Market Positioning (AI Insights)
  targetClients         String[]  // Target client types
  competitiveAdvantage  Json?     // What makes this expert unique
  marketPosition        Json?     // Position in Syrian market
  
  // Profile Status & Publishing
  profileStatus         String    @default("DRAFT") // DRAFT, REVIEW, PUBLISHED, SUSPENDED
  publishedAt           DateTime?
  lastOptimizedAt       DateTime?
  
  // Metadata
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  @@map("expert_profiles")
}
```

---

## 🏗️ **DATA STRUCTURES**

### **LocalizedString Structure**
```typescript
interface LocalizedString {
  ar: string;      // Arabic text (primary)
  en?: string;     // English translation (optional)
  generated?: boolean; // Was this AI generated?
}
```

### **Service Offering Structure**
```typescript
interface ServiceOffering {
  id: string;
  title: LocalizedString;
  description: LocalizedString;
  category: string;
  subcategory?: string;
  pricing: {
    type: 'FIXED' | 'HOURLY' | 'PACKAGE';
    basic: {
      price: number;
      currency: string;
      deliveryTime: number; // days
      features: string[];
      revisions: number;
    };
    standard?: {
      price: number;
      currency: string;
      deliveryTime: number;
      features: string[];
      revisions: number;
    };
    premium?: {
      price: number;
      currency: string;
      deliveryTime: number;
      features: string[];
      revisions: number;
    };
  };
  requirements?: string[];
  addOns?: Array<{
    name: string;
    price: number;
    description: string;
  }>;
  tags: string[];
  aiGenerated: boolean;
}
```

### **Portfolio Item Structure**
```typescript
interface PortfolioItem {
  id: string;
  title: LocalizedString;
  description: LocalizedString;
  category: string;
  subcategory?: string;
  images: string[];
  technologies: string[];
  projectUrl?: string;
  clientType: string;
  projectDuration: number; // days
  completionDate: string;
  results: string[];
  challenges: string[];
  solutions: string[];
  clientTestimonial?: string;
  featured: boolean;
  aiGenerated: boolean;
}
```

### **Industry Experience Structure**
```typescript
interface IndustryExperience {
  industry: string;
  yearsOfExperience: number;
  projectCount: number;
  specializations: string[];
  notableClients?: string[];
  keyAchievements: string[];
}
```

### **Working Hours Structure**
```typescript
interface WorkingHours {
  timezone: string;
  schedule: {
    sunday: { available: boolean; startTime?: string; endTime?: string; };
    monday: { available: boolean; startTime?: string; endTime?: string; };
    tuesday: { available: boolean; startTime?: string; endTime?: string; };
    wednesday: { available: boolean; startTime?: string; endTime?: string; };
    thursday: { available: boolean; startTime?: string; endTime?: string; };
    friday: { available: boolean; startTime?: string; endTime?: string; };
    saturday: { available: boolean; startTime?: string; endTime?: string; };
  };
  flexibility: 'STRICT' | 'FLEXIBLE' | 'VERY_FLEXIBLE';
  urgentAvailable: boolean;
  weekendAvailable: boolean;
}
```

---

## 🔄 **AI CONVERSATION TO PROFILE MAPPING**

### **Data Extraction Mapping**
```typescript
interface AIToProfileMapping {
  // From AI Conversation → Expert Profile
  professionalTitle: "title.ar",
  specialization: "specializations[0]",
  skills: "skills[]",
  experience: "experience + experienceYears",
  services: "serviceOfferings[]",
  pricing: "hourlyRate + pricingStrategy",
  portfolio: "portfolioItems[]",
  availability: "workingHours + availability",
  education: "education[]",
  certifications: "certifications[]"
}
```

### **Profile Generation Algorithm**
```typescript
interface ProfileGenerationProcess {
  step1: "Extract structured data from AI conversation";
  step2: "Validate and normalize extracted data";
  step3: "Generate compelling Arabic descriptions";
  step4: "Create service offerings with competitive pricing";
  step5: "Structure portfolio items with Syrian market context";
  step6: "Calculate profile completeness and quality scores";
  step7: "Apply Syrian cultural and business context";
  step8: "Generate SEO-optimized content for discovery";
}
```

---

## 📈 **PROFILE QUALITY METRICS**

### **Completeness Score Calculation**
```typescript
interface CompletenessMetrics {
  basicInfo: 20;        // Name, title, description
  skills: 15;           // Skills and expertise
  experience: 15;       // Experience and background
  services: 20;         // Service offerings
  portfolio: 15;        // Portfolio items
  pricing: 10;          // Pricing information
  availability: 5;      // Working hours and availability
}
```

### **Quality Score Factors**
```typescript
interface QualityFactors {
  descriptionQuality: 25;    // Compelling, well-written descriptions
  skillRelevance: 20;        // Skills match market demand
  pricingCompetitiveness: 15; // Competitive pricing strategy
  portfolioStrength: 20;     // Strong portfolio items
  marketPositioning: 10;     // Clear value proposition
  culturalRelevance: 10;     // Syrian market appropriateness
}
```

---

## 🔒 **SECURITY & PRIVACY**

### **Data Protection**
- **Encryption**: All profile data encrypted at rest
- **Access Control**: Row-level security for profile access
- **Privacy**: User control over profile visibility
- **Audit Trail**: Track all profile modifications

### **RLS Policies**
```sql
-- Expert can view and edit own profile
CREATE POLICY "Expert profile access" ON expert_profiles
    FOR ALL USING (
        auth.uid() = user_id OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Public can view published profiles
CREATE POLICY "Public profile view" ON expert_profiles
    FOR SELECT USING (profile_status = 'PUBLISHED');
```

---

## 📊 **PERFORMANCE OPTIMIZATION**

### **Indexing Strategy**
```sql
-- Profile search and filtering
CREATE INDEX idx_expert_profiles_skills ON expert_profiles USING GIN (skills);
CREATE INDEX idx_expert_profiles_categories ON expert_profiles USING GIN (service_categories);
CREATE INDEX idx_expert_profiles_location ON expert_profiles ((user.location->>'city'));
CREATE INDEX idx_expert_profiles_rating ON expert_profiles (rating DESC);
CREATE INDEX idx_expert_profiles_hourly_rate ON expert_profiles (hourly_rate);

-- AI-related queries
CREATE INDEX idx_expert_profiles_ai_generated ON expert_profiles (ai_generated);
CREATE INDEX idx_expert_profiles_completeness ON expert_profiles (profile_completeness DESC);
CREATE INDEX idx_expert_profiles_quality ON expert_profiles (profile_quality_score DESC);
```

### **Caching Strategy**
- **Profile Cache**: Cache complete profiles for 1 hour
- **Search Results**: Cache filtered results for 30 minutes
- **Statistics**: Cache profile metrics for 24 hours

---

## 🧪 **TESTING STRATEGY**

### **Data Validation Tests**
- **Profile Completeness**: Verify all required fields
- **Data Integrity**: Check foreign key relationships
- **Localization**: Validate Arabic RTL content
- **AI Integration**: Test AI-generated data quality

### **Performance Tests**
- **Profile Loading**: < 500ms for complete profile
- **Search Performance**: < 1s for filtered results
- **Bulk Operations**: Handle 1000+ profiles efficiently

---

**📝 Document Status**: ✅ Complete - Ready for Implementation
