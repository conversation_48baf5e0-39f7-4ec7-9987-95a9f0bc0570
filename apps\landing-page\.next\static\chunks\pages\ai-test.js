/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/ai-test"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cai-test.tsx&page=%2Fai-test!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cai-test.tsx&page=%2Fai-test! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/ai-test\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/ai-test.tsx */ \"./src/pages/ai-test.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/ai-test\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9QyUzQSU1Q1VzZXJzJTVDYW1lcmslNUNEb2N1bWVudHMlNUNGcmVlbGElNUNhcHBzJTVDbGFuZGluZy1wYWdlJTVDc3JjJTVDcGFnZXMlNUNhaS10ZXN0LnRzeCZwYWdlPSUyRmFpLXRlc3QhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsd0RBQXlCO0FBQ2hEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8wNWZiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvYWktdGVzdFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2FpLXRlc3QudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9haS10ZXN0XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cai-test.tsx&page=%2Fai-test!\n"));

/***/ }),

/***/ "./src/pages/ai-test.tsx":
/*!*******************************!*\
  !*** ./src/pages/ai-test.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AITestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction AITestPage() {\n    var _session_user, _session_user1;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"EXPERT\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ar\");\n    const [connectivity, setConnectivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTestingConnectivity, setIsTestingConnectivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Test AI connectivity on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        testConnectivity();\n    }, []);\n    const testConnectivity = async ()=>{\n        setIsTestingConnectivity(true);\n        try {\n            const response = await fetch(\"/api/ai/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    test: \"connectivity\"\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setConnectivity(data.data);\n            }\n        } catch (error) {\n            console.error(\"Connectivity test failed:\", error);\n        } finally{\n            setIsTestingConnectivity(false);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        try {\n            const response = await fetch(\"/api/ai/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: inputValue.trim(),\n                    userRole,\n                    language,\n                    conversationContext: {\n                        messageCount: messages.length,\n                        userRole,\n                        language\n                    }\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Request failed: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data.success && data.data) {\n                const aiMessage = {\n                    id: data.data.aiMessage.id,\n                    role: \"assistant\",\n                    content: data.data.aiMessage.content,\n                    timestamp: data.data.aiMessage.timestamp,\n                    provider: data.data.aiMessage.provider,\n                    responseTime: data.data.aiMessage.responseTime\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n            } else {\n                throw new Error(data.error || \"Failed to get AI response\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            const errorMessage = {\n                id: \"error_\".concat(Date.now()),\n                role: \"assistant\",\n                content: \"❌ خطأ: \".concat(error.message),\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsSending(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl mb-4\",\n                        children: \"يرجى تسجيل الدخول أولاً\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = \"/\",\n                        className: \"bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg\",\n                        children: \"العودة للصفحة الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: \"\\uD83E\\uDDEA اختبار الذكاء الاصطناعي - فريلا سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"اختبار النظام الذكي مع نظام الاحتياط التلقائي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-2\",\n                            children: [\n                                \"مرحباً \",\n                                (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name,\n                                \" (\",\n                                (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: \"حالة الاتصال\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testConnectivity,\n                                    disabled: isTestingConnectivity,\n                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-4 py-2 rounded-lg text-white text-sm\",\n                                    children: isTestingConnectivity ? \"جاري الاختبار...\" : \"اختبار الاتصال\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        connectivity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg \".concat(connectivity.openrouter.success ? \"bg-green-900/30 border-green-500\" : \"bg-red-900/30 border-red-500\", \" border\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: \"OpenRouter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm \".concat(connectivity.openrouter.success ? \"text-green-400\" : \"text-red-400\"),\n                                            children: connectivity.openrouter.success ? \"✅ متصل\" : \"❌ غير متصل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        connectivity.openrouter.details.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-300 mt-1\",\n                                            children: connectivity.openrouter.details.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg \".concat(connectivity.openai.success ? \"bg-green-900/30 border-green-500\" : \"bg-red-900/30 border-red-500\", \" border\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2\",\n                                            children: \"OpenAI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm \".concat(connectivity.openai.success ? \"text-green-400\" : \"text-red-400\"),\n                                            children: connectivity.openai.success ? \"✅ متصل\" : \"❌ غير متصل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        connectivity.openai.details.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-300 mt-1\",\n                                            children: connectivity.openai.details.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-white mb-2\",\n                                        children: \"نوع المستخدم\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: userRole,\n                                        onChange: (e)=>setUserRole(e.target.value),\n                                        className: \"w-full bg-gray-700 text-white rounded-lg px-4 py-2 border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"EXPERT\",\n                                                children: \"خبير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CLIENT\",\n                                                children: \"عميل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-white mb-2\",\n                                        children: \"اللغة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: language,\n                                        onChange: (e)=>setLanguage(e.target.value),\n                                        className: \"w-full bg-gray-700 text-white rounded-lg px-4 py-2 border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ar\",\n                                                children: \"العربية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"en\",\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border border-gray-700 min-h-[400px] max-h-[600px] overflow-y-auto\",\n                    children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-400 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"ابدأ محادثة جديدة مع الذكاء الاصطناعي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-4 rounded-xl \".concat(message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-700 text-white\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs opacity-70 mt-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: new Date(message.timestamp).toLocaleTimeString(\"ar-SY\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 23\n                                                }, this),\n                                                message.provider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-600 px-2 py-1 rounded\",\n                                                    children: message.provider\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 25\n                                                }, this),\n                                                message.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gray-600 px-2 py-1 rounded\",\n                                                    children: [\n                                                        message.responseTime,\n                                                        \"ms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 19\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: inputValue,\n                                onChange: (e)=>setInputValue(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"اكتب رسالتك هنا...\",\n                                className: \"flex-1 bg-gray-700 text-white rounded-lg px-4 py-3 border border-gray-600 resize-none\",\n                                rows: 3,\n                                disabled: isSending\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !inputValue.trim() || isSending,\n                                className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-6 py-3 rounded-lg text-white font-semibold\",\n                                children: isSending ? \"جاري الإرسال...\" : \"إرسال\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-test.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(AITestPage, \"Fvzv0B0lZljHyQzwuzcpj+MqcBs=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = AITestPage;\nvar _c;\n$RefreshReg$(_c, \"AITestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/ai-test.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cai-test.tsx&page=%2Fai-test!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);