# 🔌 AI Onboarding API Endpoints Specification
## Freela Syria - Backend Integration Points

> **Document Version**: 1.0  
> **Last Updated**: 2025-01-23  
> **Base URL**: `/api/v1`  
> **Authentication**: JWT + Google OAuth  
> **Language Support**: Arabic (Primary), English  

---

## 🎯 **API OVERVIEW**

### **Endpoint Categories**
1. **Authentication & User Management** - User auth and role setup
2. **AI Conversation Management** - Chat session handling
3. **Profile Generation & Management** - Expert profile CRUD
4. **Data Collection & Validation** - Form data processing
5. **Experts Directory** - Public profile discovery

---

## 🔐 **AUTHENTICATION ENDPOINTS**

### **POST /auth/google-oauth**
**Purpose**: Handle Google OAuth authentication and user creation
```typescript
// Request
interface GoogleOAuthRequest {
  googleToken: string;
  role: 'EXPERT' | 'CLIENT';
  redirectUrl?: string;
}

// Response
interface GoogleOAuthResponse {
  success: boolean;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: 'EXPERT' | 'CLIENT';
    hasCompletedOnboarding: boolean;
    dataCollected: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
  nextStep: 'DATA_COLLECTION' | 'AI_ONBOARDING' | 'DASHBOARD';
}
```

### **POST /auth/complete-data-collection**
**Purpose**: Save enhanced user data before AI conversation
```typescript
// Request
interface DataCollectionRequest {
  fullName: string;
  phoneNumber: string; // Syrian format validation
  governorate: string;
  city: string;
  servicePreferences: string[]; // For experts
  projectTypes?: string[]; // For clients
  businessInfo?: {
    isBusinessAccount: boolean;
    companyName?: string;
    companySize?: string;
  };
}

// Response
interface DataCollectionResponse {
  success: boolean;
  user: UserProfile;
  aiSessionReady: boolean;
  nextStep: 'AI_ONBOARDING';
}
```

---

## 🤖 **AI CONVERSATION ENDPOINTS**

### **POST /ai/start-onboarding-session**
**Purpose**: Initialize AI conversation session for expert onboarding
```typescript
// Request
interface StartAISessionRequest {
  userRole: 'EXPERT' | 'CLIENT';
  language: 'ar' | 'en';
  preferences?: {
    conversationStyle: 'FORMAL' | 'CASUAL';
    detailLevel: 'BASIC' | 'DETAILED';
  };
}

// Response
interface StartAISessionResponse {
  success: boolean;
  session: {
    id: string;
    status: 'ACTIVE';
    currentStep: string;
    totalSteps: number;
    completionRate: number;
  };
  initialMessage: {
    content: string;
    type: 'WELCOME';
    timestamp: string;
  };
}
```

### **POST /ai/send-message**
**Purpose**: Send user message and receive AI response
```typescript
// Request
interface SendMessageRequest {
  sessionId: string;
  message: string;
  messageType: 'TEXT' | 'VOICE' | 'IMAGE';
  metadata?: {
    voiceData?: string; // Base64 encoded audio
    imageData?: string; // Base64 encoded image
  };
}

// Response
interface SendMessageResponse {
  success: boolean;
  aiResponse: {
    content: string;
    type: 'QUESTION' | 'CLARIFICATION' | 'SUMMARY' | 'COMPLETION';
    timestamp: string;
    confidence: number;
  };
  session: {
    currentStep: string;
    completionRate: number;
    extractedData: any;
  };
  profilePreview?: ExpertProfilePreview;
}
```

### **GET /ai/session/{sessionId}/status**
**Purpose**: Get current session status and progress
```typescript
// Response
interface SessionStatusResponse {
  success: boolean;
  session: {
    id: string;
    status: 'ACTIVE' | 'COMPLETED' | 'ABANDONED';
    currentStep: string;
    totalSteps: number;
    completionRate: number;
    startedAt: string;
    lastActivityAt: string;
  };
  extractedData: any;
  messages: Array<{
    id: string;
    type: 'USER' | 'AI';
    content: string;
    timestamp: string;
  }>;
}
```

### **POST /ai/generate-profile**
**Purpose**: Generate complete expert profile from conversation data
```typescript
// Request
interface GenerateProfileRequest {
  sessionId: string;
  userReview?: {
    additionalInfo?: string;
    corrections?: any;
  };
}

// Response
interface GenerateProfileResponse {
  success: boolean;
  profile: {
    id: string;
    title: LocalizedString;
    description: LocalizedString;
    skills: string[];
    experience: string;
    serviceOfferings: ServiceOffering[];
    portfolioItems: PortfolioItem[];
    pricing: PricingInfo;
    availability: AvailabilityInfo;
    completenessScore: number;
    qualityScore: number;
  };
  aiMetrics: {
    confidenceScore: number;
    dataQuality: number;
    marketRelevance: number;
  };
}
```

---

## 👤 **PROFILE MANAGEMENT ENDPOINTS**

### **GET /profiles/expert/{userId}**
**Purpose**: Get expert profile (own or public view)
```typescript
// Response
interface ExpertProfileResponse {
  success: boolean;
  profile: {
    id: string;
    userId: string;
    title: LocalizedString;
    description: LocalizedString;
    skills: string[];
    experience: string;
    experienceYears: number;
    hourlyRate: number;
    availability: AvailabilityInfo;
    rating: number;
    completedProjects: number;
    verified: boolean;
    serviceOfferings: ServiceOffering[];
    portfolioItems: PortfolioItem[];
    profileStatus: 'DRAFT' | 'PUBLISHED';
    completenessScore: number;
    aiGenerated: boolean;
  };
  isOwner: boolean;
  canEdit: boolean;
}
```

### **PUT /profiles/expert/{userId}**
**Purpose**: Update expert profile (authenticated user only)
```typescript
// Request
interface UpdateExpertProfileRequest {
  title?: LocalizedString;
  description?: LocalizedString;
  skills?: string[];
  hourlyRate?: number;
  availability?: AvailabilityInfo;
  serviceOfferings?: ServiceOffering[];
  portfolioItems?: PortfolioItem[];
}

// Response
interface UpdateExpertProfileResponse {
  success: boolean;
  profile: ExpertProfile;
  validationErrors?: string[];
  completenessScore: number;
}
```

### **POST /profiles/expert/{userId}/publish**
**Purpose**: Publish expert profile to public directory
```typescript
// Request
interface PublishProfileRequest {
  termsAccepted: boolean;
  profileReviewed: boolean;
}

// Response
interface PublishProfileResponse {
  success: boolean;
  profile: {
    id: string;
    profileStatus: 'PUBLISHED';
    publishedAt: string;
    profileUrl: string;
  };
  message: string;
}
```

---

## 🔍 **EXPERTS DIRECTORY ENDPOINTS**

### **GET /experts/directory**
**Purpose**: Get paginated list of published expert profiles
```typescript
// Query Parameters
interface ExpertsDirectoryQuery {
  page?: number;
  limit?: number;
  skills?: string[]; // Filter by skills
  category?: string; // Service category
  location?: string; // City or governorate
  minRating?: number;
  maxHourlyRate?: number;
  availability?: 'AVAILABLE' | 'BUSY';
  sortBy?: 'RATING' | 'PRICE' | 'EXPERIENCE' | 'RECENT';
  search?: string; // Text search
}

// Response
interface ExpertsDirectoryResponse {
  success: boolean;
  experts: Array<{
    id: string;
    userId: string;
    name: string;
    title: LocalizedString;
    description: LocalizedString;
    avatar?: string;
    skills: string[];
    experience: string;
    hourlyRate: number;
    rating: number;
    completedProjects: number;
    responseTime: string;
    location: string;
    verified: boolean;
    availability: 'AVAILABLE' | 'BUSY';
    profileUrl: string;
  }>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalExperts: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    availableSkills: string[];
    availableCategories: string[];
    priceRange: { min: number; max: number; };
  };
}
```

### **GET /experts/{expertId}/public**
**Purpose**: Get public expert profile for discovery
```typescript
// Response
interface PublicExpertProfileResponse {
  success: boolean;
  expert: {
    id: string;
    name: string;
    title: LocalizedString;
    description: LocalizedString;
    avatar?: string;
    skills: string[];
    experience: string;
    experienceYears: number;
    hourlyRate: number;
    rating: number;
    reviewCount: number;
    completedProjects: number;
    responseTime: string;
    location: string;
    verified: boolean;
    joinDate: string;
    serviceOfferings: ServiceOffering[];
    portfolioItems: PortfolioItem[];
    testimonials: Testimonial[];
    availability: AvailabilityInfo;
  };
  relatedExperts: Array<{
    id: string;
    name: string;
    title: string;
    rating: number;
    hourlyRate: number;
  }>;
}
```

---

## 📊 **ANALYTICS & INSIGHTS ENDPOINTS**

### **GET /analytics/profile-performance**
**Purpose**: Get profile performance metrics (authenticated expert only)
```typescript
// Response
interface ProfilePerformanceResponse {
  success: boolean;
  metrics: {
    profileViews: number;
    profileClicks: number;
    contactRequests: number;
    conversionRate: number;
    searchRanking: number;
    completenessScore: number;
    qualityScore: number;
  };
  trends: {
    viewsLastWeek: number[];
    clicksLastWeek: number[];
    contactsLastWeek: number[];
  };
  recommendations: Array<{
    type: string;
    title: string;
    description: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH';
    actionRequired: string;
  }>;
}
```

---

## 🔧 **UTILITY ENDPOINTS**

### **GET /utils/syrian-locations**
**Purpose**: Get list of Syrian governorates and cities
```typescript
// Response
interface SyrianLocationsResponse {
  success: boolean;
  locations: Array<{
    governorate: string;
    governorateAr: string;
    cities: Array<{
      name: string;
      nameAr: string;
      population?: number;
    }>;
  }>;
}
```

### **GET /utils/service-categories**
**Purpose**: Get available service categories for experts
```typescript
// Response
interface ServiceCategoriesResponse {
  success: boolean;
  categories: Array<{
    id: string;
    name: LocalizedString;
    description: LocalizedString;
    icon: string;
    subcategories: Array<{
      id: string;
      name: LocalizedString;
      description: LocalizedString;
    }>;
    popularSkills: string[];
    averageHourlyRate: number;
  }>;
}
```

---

## 🚨 **ERROR HANDLING**

### **Standard Error Response**
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}
```

### **Common Error Codes**
- `AUTH_REQUIRED` - Authentication required
- `INVALID_TOKEN` - Invalid or expired token
- `PERMISSION_DENIED` - Insufficient permissions
- `VALIDATION_ERROR` - Request validation failed
- `AI_SERVICE_ERROR` - AI service unavailable
- `PROFILE_NOT_FOUND` - Profile doesn't exist
- `SESSION_EXPIRED` - AI session expired
- `RATE_LIMIT_EXCEEDED` - Too many requests

---

## 🔒 **SECURITY CONSIDERATIONS**

### **Authentication**
- **JWT Tokens**: Secure token-based authentication
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Sanitize all inputs
- **CORS**: Proper cross-origin configuration

### **Data Protection**
- **Encryption**: All sensitive data encrypted
- **Privacy**: User data access controls
- **Audit Logging**: Track all API calls
- **GDPR Compliance**: Data deletion capabilities

---

**📝 Document Status**: ✅ Complete - Ready for Implementation
