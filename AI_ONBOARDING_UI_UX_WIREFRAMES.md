# 🎨 AI Onboarding UI/UX Wireframes & Design Specifications
## Freela Syria - Interface Design for Expert Profile Creation

> **Document Version**: 1.0  
> **Last Updated**: 2025-01-23  
> **Design System**: Glass Morphism + Syrian Cultural Elements  
> **Language**: Arabic RTL (Primary), English Support  
> **Responsive**: Mobile-First Design  

---

## 🎯 **DESIGN OBJECTIVES**

### **Core Principles**
1. **Intuitive Flow**: Seamless progression from auth to published profile
2. **Cultural Sensitivity**: Syrian market context and Arabic typography
3. **Glass Morphism**: Consistent with landing page design language
4. **Accessibility**: WCAG 2.1 AA compliance with RTL support
5. **Mobile-First**: Optimized for Syrian mobile usage patterns

### **Visual Identity**
- **Typography**: Cairo/Tajawal fonts for Arabic, Inter for English
- **Colors**: Dark theme with Syrian cultural accents (#CE1126, #007A3D)
- **Effects**: Backdrop blur, subtle gradients, smooth animations
- **Layout**: RTL-first with responsive breakpoints

---

## 📱 **COMPONENT SPECIFICATIONS**

### **1. Enhanced Data Collection Form**

#### **Layout Structure**
```
┌─────────────────────────────────────────┐
│  🇸🇾 فريلا سوريا - إكمال البيانات        │
├─────────────────────────────────────────┤
│                                         │
│  [Progress Bar: 25% Complete]           │
│                                         │
│  📝 معلوماتك الأساسية                    │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ الاسم الكامل *                      │ │
│  │ [أحمد محمد]                        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ رقم الهاتف *                       │ │
│  │ [+963 ] [___________]              │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ المحافظة *                         │ │
│  │ [دمشق ▼]                          │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ المدينة *                          │ │
│  │ [دمشق ▼]                          │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  📋 تفضيلات الخدمة                      │
│                                         │
│  [💻 تطوير الويب] [🎨 التصميم]         │
│  [📱 تطوير التطبيقات] [📊 التسويق]     │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ ☐ حساب تجاري                       │ │
│  │   └─ اسم الشركة: [_____________]    │ │
│  └─────────────────────────────────────┘ │
│                                         │
│           [متابعة إلى الدردشة] ←        │
│                                         │
└─────────────────────────────────────────┘
```

#### **Component Features**
- **Glass Morphism Card**: Backdrop blur with subtle border
- **Progressive Disclosure**: Business fields appear when toggled
- **Real-time Validation**: Instant feedback on field completion
- **Syrian Phone Format**: Automatic +963 prefix with validation
- **Dynamic City Dropdown**: Cities update based on governorate selection
- **Multi-select Tags**: Visual service preference selection

---

### **2. AI Chat Interface**

#### **Main Chat Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ ← فريلا سوريا | الذكاء الاصطناعي                    [⚙️] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                Profile Preview Sidebar                  │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 👤 أحمد محمد                                       │ │ │
│ │ │ 💼 مطور ويب متخصص في React                       │ │ │
│ │ │                                                     │ │ │
│ │ │ 🎯 المهارات                                        │ │ │
│ │ │ [React] [Node.js] [TypeScript]                     │ │ │
│ │ │                                                     │ │ │
│ │ │ 💰 السعر: 25 $/ساعة                               │ │ │
│ │ │                                                     │ │ │
│ │ │ ┌─────────────────────────────────────────────────┐ │ │ │
│ │ │ │ اكتمال الملف: 65%                              │ │ │ │
│ │ │ │ ████████████░░░░░░░░                            │ │ │ │
│ │ │ └─────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   Chat Messages Area                    │ │
│ │                                                         │ │
│ │ 🤖 مرحباً أحمد! أهلاً بك في فريلا سوريا               │ │
│ │    أرى أنك من دمشق وتريد الانضمام كخبير...           │ │
│ │                                          [👍] [👎]     │ │
│ │                                                         │ │
│ │                              أهلاً! نعم، أنا مطور ويب 👤 │ │
│ │                                                         │ │
│ │ 🤖 ممتاز! ما هو تخصصك الرئيسي؟                       │ │
│ │    • تطوير المواقع والتطبيقات                         │ │
│ │    • التصميم الجرافيكي                                │ │
│ │    • أو أخبرني بتخصصك...                              │ │
│ │                                                         │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 🎤 🖼️ [اكتب رسالتك هنا...]              [إرسال] │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### **Chat Interface Features**
- **Split Layout**: Chat on left, profile preview on right (RTL)
- **Real-time Updates**: Profile preview updates as data is collected
- **Message Reactions**: Thumbs up/down for AI message quality
- **Voice Input**: Microphone button for voice messages
- **Image Upload**: Camera/gallery button for portfolio images
- **Typing Indicators**: Show when AI is processing
- **Message Timestamps**: Arabic-formatted time display

---

### **3. Profile Review & Editing Interface**

#### **Profile Review Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ مراجعة الملف الشخصي                              [حفظ] [نشر] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📝 المعلومات الأساسية                        [تعديل] │ │
│ │                                                         │ │
│ │ 👤 أحمد محمد                                           │ │
│ │ 💼 مطور ويب متخصص في React و Node.js                 │ │
│ │ 📍 دمشق، سوريا                                        │ │
│ │                                                         │ │
│ │ 📄 الوصف المهني:                                      │ │
│ │ مطور ويب محترف مع أكثر من 5 سنوات من الخبرة...      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🎯 المهارات والخبرات                         [تعديل] │ │
│ │                                                         │ │
│ │ [React] [Node.js] [TypeScript] [PostgreSQL]            │ │
│ │ [MongoDB] [AWS] [+ إضافة مهارة]                       │ │
│ │                                                         │ │
│ │ 📊 مستوى الخبرة: متقدم (5 سنوات)                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 💼 الخدمات المقدمة                           [تعديل] │ │
│ │                                                         │ │
│ │ 🌐 تطوير موقع ويب كامل                               │ │
│ │    💰 500-2000$ | ⏱️ 7-14 يوم                         │ │
│ │                                                         │ │
│ │ 🎨 تصميم واجهة مستخدم                                │ │
│ │    💰 200-800$ | ⏱️ 3-7 أيام                          │ │
│ │                                                         │ │
│ │ [+ إضافة خدمة جديدة]                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🎨 معرض الأعمال                              [تعديل] │ │
│ │                                                         │ │
│ │ ┌─────┐ ┌─────┐ ┌─────┐                                │ │
│ │ │ 🖼️  │ │ 🖼️  │ │ 🖼️  │                                │ │
│ │ │     │ │     │ │     │                                │ │
│ │ └─────┘ └─────┘ └─────┘                                │ │
│ │                                                         │ │
│ │ [+ إضافة عمل جديد]                                    │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ⏰ التوفر والأسعار                           [تعديل] │ │
│ │                                                         │ │
│ │ 💰 السعر بالساعة: 25$ - 40$                           │ │
│ │ ⏱️ وقت الاستجابة: خلال ساعتين                        │ │
│ │ 📅 ساعات العمل: الأحد-الخميس، 9ص-5م                 │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ✅ جاهز للنشر؟                                        │ │
│ │                                                         │ │
│ │ ☑️ راجعت جميع المعلومات                               │ │
│ │ ☑️ أوافق على شروط الخدمة                             │ │
│ │ ☑️ أوافق على سياسة الخصوصية                          │ │
│ │                                                         │ │
│ │           [نشر الملف الشخصي] 🚀                       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### **Editing Features**
- **Inline Editing**: Click any section to edit in place
- **Visual Feedback**: Highlight edited sections
- **Auto-save**: Save changes automatically
- **Validation**: Real-time validation with error messages
- **Preview Mode**: Toggle between edit and preview modes

---

### **4. Experts Directory Interface**

#### **Directory Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ دليل الخبراء                                    [🔍] [⚙️] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔍 البحث والفلترة                                      │ │
│ │                                                         │ │
│ │ [ابحث عن خبير...]                          [بحث] 🔍   │ │
│ │                                                         │ │
│ │ 🏷️ المهارات: [الكل ▼] 💰 السعر: [الكل ▼]              │ │
│ │ 📍 الموقع: [الكل ▼] ⭐ التقييم: [الكل ▼]              │ │
│ │                                                         │ │
│ │ 📊 ترتيب حسب: [التقييم ▼] [الأحدث] [السعر]            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📊 النتائج: 127 خبير                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 👤 أحمد محمد                              ⭐ 4.8 (23) │ │
│ │ 💼 مطور ويب متخصص في React و Node.js                 │ │
│ │ 📍 دمشق، سوريا | 💰 25$/ساعة | ⏱️ يرد خلال ساعتين    │ │
│ │                                                         │ │
│ │ 🏷️ [React] [Node.js] [TypeScript] [PostgreSQL]        │ │
│ │                                                         │ │
│ │ "مطور ويب محترف مع أكثر من 5 سنوات من الخبرة..."     │ │
│ │                                                         │ │
│ │ ✅ متاح الآن | 🎯 47 مشروع مكتمل                      │ │
│ │                                                         │ │
│ │                    [عرض الملف] [تواصل معي] ←           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 👤 سارة أحمد                              ⭐ 4.9 (31) │ │
│ │ 💼 مصممة جرافيك ومطورة واجهات مستخدم                │ │
│ │ 📍 حلب، سوريا | 💰 20$/ساعة | ⏱️ يرد خلال 30 دقيقة   │ │
│ │                                                         │ │
│ │ 🏷️ [Photoshop] [Illustrator] [Figma] [UI/UX]          │ │
│ │                                                         │ │
│ │ "مصممة إبداعية متخصصة في تصميم الهوية البصرية..."    │ │
│ │                                                         │ │
│ │ ✅ متاحة الآن | 🎯 62 مشروع مكتمل                     │ │
│ │                                                         │ │
│ │                    [عرض الملف] [تواصل معي] ←           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │              [← السابق] 1 2 3 4 5 [التالي →]           │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### **Directory Features**
- **Advanced Filtering**: Multiple filter criteria
- **Real-time Search**: Instant search results
- **Expert Cards**: Comprehensive expert information
- **Availability Status**: Real-time availability indicators
- **Pagination**: Efficient navigation through results
- **Responsive Grid**: Adapts to screen size

---

## 🎨 **DESIGN SYSTEM SPECIFICATIONS**

### **Color Palette**
```css
/* Primary Colors */
--primary-gold: #D4AF37;
--primary-dark: #1a1a2e;
--primary-purple: #16213e;

/* Syrian Cultural Colors */
--syrian-red: #CE1126;
--syrian-green: #007A3D;
--syrian-white: #FFFFFF;

/* Glass Morphism */
--glass-bg: rgba(255, 255, 255, 0.1);
--glass-border: rgba(255, 255, 255, 0.2);
--glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

/* Text Colors */
--text-primary: #FFFFFF;
--text-secondary: rgba(255, 255, 255, 0.8);
--text-muted: rgba(255, 255, 255, 0.6);
```

### **Typography Scale**
```css
/* Arabic Typography - Cairo/Tajawal */
--font-family-ar: 'Cairo', 'Tajawal', sans-serif;
--font-family-en: 'Inter', sans-serif;

/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### **Spacing System**
```css
/* Spacing Scale */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
```

### **Animation Specifications**
```css
/* Transition Durations */
--duration-fast: 150ms;
--duration-normal: 300ms;
--duration-slow: 500ms;

/* Easing Functions */
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* Glass Morphism Effects */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
}
```

---

## 📱 **RESPONSIVE BREAKPOINTS**

### **Mobile-First Approach**
```css
/* Breakpoints */
--mobile: 320px;      /* Small phones */
--mobile-lg: 480px;   /* Large phones */
--tablet: 768px;      /* Tablets */
--desktop: 1024px;    /* Small desktops */
--desktop-lg: 1280px; /* Large desktops */
--desktop-xl: 1536px; /* Extra large screens */
```

### **Layout Adaptations**
- **Mobile (320-767px)**: Single column, stacked components
- **Tablet (768-1023px)**: Two-column layout for chat interface
- **Desktop (1024px+)**: Full three-column layout with sidebar

---

## ♿ **ACCESSIBILITY FEATURES**

### **WCAG 2.1 AA Compliance**
- **Color Contrast**: Minimum 4.5:1 ratio for normal text
- **Focus Indicators**: Visible focus states for all interactive elements
- **Screen Reader**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility
- **Text Scaling**: Support up to 200% zoom without horizontal scrolling

### **RTL Support**
- **Text Direction**: Proper RTL text flow for Arabic
- **Layout Mirroring**: Interface elements mirror for RTL languages
- **Icon Orientation**: Directional icons flip appropriately
- **Form Alignment**: Form fields align to the right for Arabic

---

**📝 Document Status**: ✅ Complete - Ready for Implementation
