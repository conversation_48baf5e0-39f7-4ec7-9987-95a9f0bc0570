"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai/chat";
exports.ids = ["pages/api/ai/chat"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cchat.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cchat.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\ai\\chat.ts */ \"(api)/./src/pages/api/ai/chat.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai/chat\",\n        pathname: \"/api/ai/chat\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cchat.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/ai/chat.ts":
/*!**********************************!*\
  !*** ./src/pages/api/ai/chat.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler),\n/* harmony export */   testAIConnectivity: () => (/* binding */ testAIConnectivity)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _services_aiService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/aiService */ \"(api)/./src/services/aiService.ts\");\n\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            success: false,\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Get user session\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user?.id) {\n            return res.status(401).json({\n                success: false,\n                message: \"Unauthorized\"\n            });\n        }\n        const { message, userRole, language = \"ar\", conversationContext = {}, sessionId } = req.body;\n        // Validate required fields\n        if (!message || !userRole) {\n            return res.status(400).json({\n                success: false,\n                message: \"Missing required fields: message, userRole\"\n            });\n        }\n        console.log(`🤖 Processing AI chat request for ${userRole} in ${language}`);\n        console.log(`📝 Message: ${message.substring(0, 100)}...`);\n        // Build conversation messages\n        const systemMessage = _services_aiService__WEBPACK_IMPORTED_MODULE_2__.aiService.buildSystemMessage(userRole, language, conversationContext);\n        const messages = [\n            systemMessage,\n            {\n                role: \"user\",\n                content: message\n            }\n        ];\n        // Generate AI response with intelligent fallback\n        const startTime = Date.now();\n        const aiResponse = await _services_aiService__WEBPACK_IMPORTED_MODULE_2__.aiService.generateResponse(messages, userRole, language, conversationContext, {\n            temperature: 0.8,\n            max_tokens: 800,\n            timeout: 12000 // 12 second timeout\n        });\n        const responseId = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const currentSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        console.log(`✅ AI response generated via ${aiResponse.provider} in ${aiResponse.responseTime}ms`);\n        // Return successful response\n        return res.status(200).json({\n            success: true,\n            message: \"AI response generated successfully\",\n            data: {\n                aiMessage: {\n                    id: responseId,\n                    role: \"assistant\",\n                    content: aiResponse.content,\n                    timestamp: new Date().toISOString(),\n                    provider: aiResponse.provider,\n                    responseTime: aiResponse.responseTime\n                },\n                sessionId: currentSessionId,\n                conversationContext: {\n                    ...conversationContext,\n                    lastProvider: aiResponse.provider,\n                    messageCount: (conversationContext.messageCount || 0) + 1,\n                    totalResponseTime: (conversationContext.totalResponseTime || 0) + aiResponse.responseTime\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Error in AI chat endpoint:\", error);\n        // Return error response\n        return res.status(500).json({\n            success: false,\n            message: \"Failed to generate AI response\",\n            error: error.message\n        });\n    }\n}\n/**\n * Test endpoint for AI service connectivity\n */ async function testAIConnectivity(req, res) {\n    try {\n        const results = await _services_aiService__WEBPACK_IMPORTED_MODULE_2__.aiService.testConnectivity();\n        return res.status(200).json({\n            success: true,\n            message: \"AI connectivity test completed\",\n            data: results\n        });\n    } catch (error) {\n        return res.status(500).json({\n            success: false,\n            message: \"AI connectivity test failed\",\n            error: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/ai/chat.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, trigger, session }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email,\n                trigger\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || user.email || \"temp-id\"; // Use email as fallback ID\n                token.role = undefined; // CRITICAL FIX: No default role - user must select during onboarding\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image || null;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            // Handle onboarding completion updates\n            if (trigger === \"update\" && session) {\n                console.log(\"\\uD83D\\uDD04 JWT token update triggered with data:\", session);\n                // Update role if provided\n                if (session.role) {\n                    token.role = session.role;\n                    console.log(\"✅ Role updated in token:\", session.role);\n                }\n                // Update onboarding completion status if provided\n                if (session.hasCompletedOnboarding !== undefined) {\n                    token.hasCompletedOnboarding = session.hasCompletedOnboarding;\n                    console.log(\"✅ Onboarding status updated in token:\", session.hasCompletedOnboarding);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl\n                });\n                // Check if this is a post-authentication callback\n                if (url.includes(\"/api/auth/callback\") || url === baseUrl || url === `${baseUrl}/`) {\n                    console.log(\"\\uD83D\\uDD04 Post-authentication callback detected\");\n                    // CRITICAL FIX: Always redirect to AI onboarding first for proper flow\n                    // The AI onboarding page will handle the logic for completed users\n                    console.log(\"\\uD83E\\uDD16 Redirecting ALL authenticated users to AI onboarding for proper flow handling\");\n                    return `${baseUrl}/ai-onboarding`;\n                }\n                // Handle relative URLs\n                if (url.startsWith(\"/\")) {\n                    const fullUrl = `${baseUrl}${url}`;\n                    console.log(\"\\uD83D\\uDD04 Converting relative URL to absolute:\", fullUrl);\n                    return fullUrl;\n                }\n                // Handle same-origin URLs\n                if (url.startsWith(baseUrl)) {\n                    console.log(\"\\uD83D\\uDD04 Same-origin URL redirect:\", url);\n                    return url;\n                }\n                // Default fallback - redirect to landing page for safety\n                console.log(\"\\uD83C\\uDFE0 Fallback redirect to landing page\");\n                return `${baseUrl}/?auth=success`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api)/./src/services/aiService.ts":
/*!***********************************!*\
  !*** ./src/services/aiService.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: () => (/* binding */ aiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _openaiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./openaiService */ \"(api)/./src/services/openaiService.ts\");\n/**\n * Intelligent AI Service with Fallback System\n * Tries OpenRouter first, then falls back to OpenAI GPT-4\n */ \nclass AIService {\n    constructor(){\n        this.openRouterBaseUrl = \"https://openrouter.ai/api/v1\";\n        this.openRouterApiKey = process.env.OPENROUTER_API_KEY || \"\";\n    }\n    /**\n   * Generate AI response with intelligent fallback\n   */ async generateResponse(messages, userRole, language = \"ar\", conversationContext = {}, options = {}) {\n        const startTime = Date.now();\n        // Try OpenRouter first (unless explicitly skipped)\n        if (!options.skipOpenRouter && this.openRouterApiKey) {\n            try {\n                console.log(\"\\uD83D\\uDD04 Trying OpenRouter API...\");\n                const openRouterResponse = await this.tryOpenRouter(messages, options);\n                return {\n                    content: openRouterResponse.content,\n                    provider: \"openrouter\",\n                    usage: openRouterResponse.usage,\n                    responseTime: Date.now() - startTime\n                };\n            } catch (error) {\n                console.warn(\"⚠️ OpenRouter failed, trying OpenAI fallback:\", error.message);\n            }\n        }\n        // Try OpenAI as fallback\n        if (_openaiService__WEBPACK_IMPORTED_MODULE_0__.openAIService.isAvailable()) {\n            try {\n                console.log(\"\\uD83D\\uDD04 Trying OpenAI API...\");\n                const userMessage = messages.find((m)=>m.role === \"user\")?.content || \"\";\n                const openAIResponse = await _openaiService__WEBPACK_IMPORTED_MODULE_0__.openAIService.generateSyrianMarketplaceResponse(userMessage, userRole, language, conversationContext, options);\n                return {\n                    content: openAIResponse.content,\n                    provider: \"openai\",\n                    usage: openAIResponse.usage,\n                    responseTime: Date.now() - startTime\n                };\n            } catch (error) {\n                console.warn(\"⚠️ OpenAI also failed:\", error.message);\n            }\n        }\n        // Final fallback to static responses\n        console.log(\"\\uD83D\\uDD04 Using static fallback response...\");\n        return {\n            content: this.generateStaticFallback(userRole, language),\n            provider: \"fallback\",\n            responseTime: Date.now() - startTime\n        };\n    }\n    /**\n   * Try OpenRouter API\n   */ async tryOpenRouter(messages, options) {\n        const { model = \"openai/gpt-4-turbo-preview\", temperature = 0.7, max_tokens = 1000, timeout = 10000 } = options;\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), timeout);\n        try {\n            const response = await fetch(`${this.openRouterBaseUrl}/chat/completions`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.openRouterApiKey}`,\n                    \"Content-Type\": \"application/json\",\n                    \"HTTP-Referer\": \"https://freela-syria.com\",\n                    \"X-Title\": \"Freela Syria AI Chat\"\n                },\n                body: JSON.stringify({\n                    model,\n                    messages,\n                    temperature,\n                    max_tokens,\n                    top_p: 0.9,\n                    frequency_penalty: 0.1,\n                    presence_penalty: 0.1\n                }),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            return {\n                content: data.choices[0]?.message?.content || \"\",\n                usage: data.usage\n            };\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error.name === \"AbortError\") {\n                throw new Error(\"OpenRouter API request timed out\");\n            }\n            throw error;\n        }\n    }\n    /**\n   * Generate static fallback response\n   */ generateStaticFallback(userRole, language) {\n        if (language === \"ar\") {\n            if (userRole === \"EXPERT\") {\n                return `🇸🇾 أهلاً بك في فريلا سوريا!\n\nعذراً، نواجه مشكلة مؤقتة في خدمة الذكاء الاصطناعي. \n\n**يمكنك المتابعة بالطرق التالية:**\n✅ أخبرني عن مهاراتك وخبراتك\n✅ اذكر نوع الخدمات التي تقدمها\n✅ حدد أسعارك المفضلة\n✅ اكتب وصف مختصر عن نفسك\n\nسأساعدك في إعداد ملفك الشخصي بمجرد عودة الخدمة للعمل.\n\n*يرجى المحاولة مرة أخرى خلال دقائق قليلة* 🔄`;\n            } else {\n                return `🇸🇾 أهلاً بك في فريلا سوريا!\n\nعذراً، نواجه مشكلة مؤقتة في خدمة الذكاء الاصطناعي.\n\n**يمكنك المتابعة بالطرق التالية:**\n✅ أخبرني عن مشروعك المطلوب\n✅ حدد نوع الخدمة التي تحتاجها\n✅ اذكر ميزانيتك المتوقعة\n✅ حدد الجدول الزمني المطلوب\n\nسأساعدك في إيجاد الخبير المناسب بمجرد عودة الخدمة للعمل.\n\n*يرجى المحاولة مرة أخرى خلال دقائق قليلة* 🔄`;\n            }\n        } else {\n            if (userRole === \"EXPERT\") {\n                return `🇸🇾 Welcome to Freela Syria!\n\nSorry, we're experiencing a temporary issue with our AI service.\n\n**You can continue with the following:**\n✅ Tell me about your skills and experience\n✅ Mention the type of services you provide\n✅ Set your preferred prices\n✅ Write a brief description about yourself\n\nI'll help you set up your profile once the service is back online.\n\n*Please try again in a few minutes* 🔄`;\n            } else {\n                return `🇸🇾 Welcome to Freela Syria!\n\nSorry, we're experiencing a temporary issue with our AI service.\n\n**You can continue with the following:**\n✅ Tell me about your required project\n✅ Specify the type of service you need\n✅ Mention your expected budget\n✅ Set the required timeline\n\nI'll help you find the right expert once the service is back online.\n\n*Please try again in a few minutes* 🔄`;\n            }\n        }\n    }\n    /**\n   * Test AI service connectivity\n   */ async testConnectivity() {\n        const results = {\n            openrouter: {\n                success: false,\n                details: {}\n            },\n            openai: {\n                success: false,\n                details: {}\n            }\n        };\n        // Test OpenRouter\n        if (this.openRouterApiKey) {\n            try {\n                const startTime = Date.now();\n                await this.tryOpenRouter([\n                    {\n                        role: \"user\",\n                        content: \"Test message\"\n                    }\n                ], {\n                    max_tokens: 10,\n                    timeout: 5000\n                });\n                results.openrouter = {\n                    success: true,\n                    details: {\n                        responseTime: Date.now() - startTime,\n                        timestamp: new Date().toISOString()\n                    }\n                };\n            } catch (error) {\n                results.openrouter = {\n                    success: false,\n                    details: {\n                        error: error.message\n                    }\n                };\n            }\n        }\n        // Test OpenAI\n        results.openai = await _openaiService__WEBPACK_IMPORTED_MODULE_0__.openAIService.testConnection();\n        return results;\n    }\n    /**\n   * Build system message for conversation context\n   */ buildSystemMessage(userRole, language, conversationContext = {}) {\n        const isArabic = language === \"ar\";\n        const basePrompt = isArabic ? `أنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية.` : `You are an AI assistant specialized in Freela Syria professional services platform.`;\n        const roleSpecificPrompt = userRole === \"EXPERT\" ? isArabic ? `تساعد الخبراء السوريين في بناء ملفاتهم المهنية وتطوير أعمالهم في السوق السوري والعربي.` : `You help Syrian experts build their professional profiles and develop their businesses in the Syrian and Arab markets.` : isArabic ? `تساعد العملاء في إيجاد الخبراء المناسبين لمشاريعهم في السوق السوري.` : `You help clients find the right experts for their projects in the Syrian market.`;\n        const culturalContext = isArabic ? `\\n\\nالسياق الثقافي: مراعاة الظروف الاقتصادية السورية، التركيز على الجودة والثقة، استخدام اللهجة المناسبة.` : `\\n\\nCultural Context: Consider Syrian economic conditions, focus on quality and trust, use appropriate communication style.`;\n        return {\n            role: \"system\",\n            content: basePrompt + \" \" + roleSpecificPrompt + culturalContext\n        };\n    }\n}\n// Export singleton instance\nconst aiService = new AIService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (aiService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/aiService.ts\n");

/***/ }),

/***/ "(api)/./src/services/openaiService.ts":
/*!***************************************!*\
  !*** ./src/services/openaiService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   openAIService: () => (/* binding */ openAIService)\n/* harmony export */ });\n/**\n * Direct OpenAI GPT-4 Service for Freela Syria\n * Fallback service when OpenRouter fails\n */ class OpenAIService {\n    constructor(){\n        this.baseUrl = \"https://api.openai.com/v1\";\n        this.apiKey = process.env.OPENAI_API_KEY || \"\";\n        if (!this.apiKey || this.apiKey === \"your-openai-api-key-here\") {\n            console.warn(\"⚠️ OpenAI API key not configured. Direct OpenAI fallback will not work.\");\n        }\n    }\n    /**\n   * Check if OpenAI service is available\n   */ isAvailable() {\n        return this.apiKey && this.apiKey !== \"your-openai-api-key-here\";\n    }\n    /**\n   * Generate AI response using OpenAI GPT-4\n   */ async generateResponse(messages, options = {}) {\n        if (!this.isAvailable()) {\n            throw new Error(\"OpenAI API key not configured\");\n        }\n        const { model = \"gpt-4-turbo-preview\", temperature = 0.7, max_tokens = 1000, timeout = 10000 } = options;\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), timeout);\n        try {\n            const response = await fetch(`${this.baseUrl}/chat/completions`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.apiKey}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    model,\n                    messages,\n                    temperature,\n                    max_tokens,\n                    top_p: 0.9,\n                    frequency_penalty: 0.1,\n                    presence_penalty: 0.1\n                }),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            return {\n                content: data.choices[0]?.message?.content || \"\",\n                usage: data.usage\n            };\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error.name === \"AbortError\") {\n                throw new Error(\"OpenAI API request timed out\");\n            }\n            console.error(\"OpenAI API error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Generate Syrian marketplace-specific AI response\n   */ async generateSyrianMarketplaceResponse(userMessage, userRole, language = \"ar\", conversationContext = {}, options = {}) {\n        const systemMessage = this.buildSyrianSystemMessage(userRole, language, conversationContext);\n        const messages = [\n            {\n                role: \"system\",\n                content: systemMessage\n            },\n            {\n                role: \"user\",\n                content: userMessage\n            }\n        ];\n        return this.generateResponse(messages, {\n            temperature: 0.8,\n            max_tokens: 800,\n            ...options\n        });\n    }\n    /**\n   * Build system message for Syrian marketplace context\n   */ buildSyrianSystemMessage(userRole, language, context) {\n        const isArabic = language === \"ar\";\n        if (userRole === \"EXPERT\") {\n            return isArabic ? `\nأنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية. تساعد الخبراء السوريين في بناء ملفاتهم المهنية وتطوير أعمالهم.\n\nالسياق الثقافي والاقتصادي:\n- السوق السوري يتطلب أسعار تنافسية ومناسبة للظروف الاقتصادية\n- التركيز على الجودة والثقة في العلاقات التجارية\n- أهمية الشبكات المهنية والتوصيات\n- التكيف مع التحديات الاقتصادية والتقنية\n\nمهامك:\n1. مساعدة الخبراء في تحديد مهاراتهم وخبراتهم\n2. اقتراح أسعار مناسبة للسوق السوري\n3. كتابة أوصاف خدمات جذابة ومهنية\n4. تقديم نصائح لبناء سمعة قوية\n5. مساعدة في استراتيجيات التسويق المحلي\n\nأسلوب التواصل:\n- ودود ومهني\n- استخدام اللهجة السورية المناسبة\n- تقديم أمثلة عملية من السوق السوري\n- التشجيع والدعم الإيجابي\n- طرح أسئلة توضيحية مفيدة\n\nتذكر: الهدف هو مساعدة الخبراء السوريين على النجاح في السوق المحلي والعربي.\n` : `\nYou are an AI assistant specialized in Freela Syria professional services platform. You help Syrian experts build their professional profiles and develop their businesses.\n\nCultural and Economic Context:\n- Syrian market requires competitive prices suitable for economic conditions\n- Focus on quality and trust in business relationships\n- Importance of professional networks and recommendations\n- Adapting to economic and technical challenges\n\nYour Tasks:\n1. Help experts identify their skills and experience\n2. Suggest appropriate prices for the Syrian market\n3. Write attractive and professional service descriptions\n4. Provide advice for building a strong reputation\n5. Assist with local marketing strategies\n\nCommunication Style:\n- Friendly and professional\n- Use appropriate Syrian dialect when needed\n- Provide practical examples from the Syrian market\n- Encouragement and positive support\n- Ask helpful clarifying questions\n\nRemember: The goal is to help Syrian experts succeed in the local and Arab markets.\n`;\n        } else {\n            return isArabic ? `\nأنت مساعد ذكي متخصص في منصة فريلا سوريا للخدمات المهنية. تساعد العملاء في إيجاد الخبراء المناسبين لمشاريعهم.\n\nالسياق الثقافي والاقتصادي:\n- فهم احتياجات السوق السوري\n- التركيز على الجودة مقابل السعر المناسب\n- أهمية التواصل الواضح والثقة\n- مراعاة الميزانيات والجداول الزمنية الواقعية\n\nمهامك:\n1. فهم متطلبات المشروع بوضوح\n2. اقتراح نوع الخدمة المناسبة\n3. مساعدة في تحديد ميزانية واقعية\n4. تقديم نصائح لاختيار الخبير المناسب\n5. إرشاد حول إدارة المشروع بنجاح\n\nأسلوب التواصل:\n- ودود ومفهوم\n- طرح أسئلة توضيحية مفيدة\n- تقديم خيارات متنوعة\n- شرح العمليات بوضوح\n- التأكد من فهم الاحتياجات\n\nتذكر: الهدف هو مساعدة العملاء في إيجاد الحلول المناسبة لمشاريعهم في السوق السوري.\n` : `\nYou are an AI assistant specialized in Freela Syria professional services platform. You help clients find the right experts for their projects.\n\nCultural and Economic Context:\n- Understanding Syrian market needs\n- Focus on quality vs appropriate pricing\n- Importance of clear communication and trust\n- Considering realistic budgets and timelines\n\nYour Tasks:\n1. Clearly understand project requirements\n2. Suggest appropriate service types\n3. Help determine realistic budgets\n4. Provide advice for choosing the right expert\n5. Guide successful project management\n\nCommunication Style:\n- Friendly and understandable\n- Ask helpful clarifying questions\n- Provide diverse options\n- Explain processes clearly\n- Ensure understanding of needs\n\nRemember: The goal is to help clients find suitable solutions for their projects in the Syrian market.\n`;\n        }\n    }\n    /**\n   * Test OpenAI API connectivity\n   */ async testConnection() {\n        if (!this.isAvailable()) {\n            return {\n                success: false,\n                details: {\n                    error: \"OpenAI API key not configured\"\n                }\n            };\n        }\n        try {\n            const startTime = Date.now();\n            const response = await this.generateResponse([\n                {\n                    role: \"user\",\n                    content: \"Test message\"\n                }\n            ], {\n                max_tokens: 10,\n                timeout: 5000\n            });\n            const responseTime = Date.now() - startTime;\n            return {\n                success: true,\n                details: {\n                    responseTime,\n                    contentLength: response.content.length,\n                    usage: response.usage,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                details: {\n                    error: error.message,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        }\n    }\n}\n// Export singleton instance\nconst openAIService = new OpenAIService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (openAIService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/openaiService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai%5Cchat.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();