import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { aiService } from '../../../services/aiService';

interface ChatRequest {
  message: string;
  userRole: 'EXPERT' | 'CLIENT';
  language?: 'ar' | 'en';
  conversationContext?: any;
  sessionId?: string;
}

interface ChatResponse {
  success: boolean;
  message: string;
  data?: {
    aiMessage: {
      id: string;
      role: 'assistant';
      content: string;
      timestamp: string;
      provider: string;
      responseTime: number;
    };
    sessionId: string;
    conversationContext?: any;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChatResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { 
      message, 
      userRole, 
      language = 'ar', 
      conversationContext = {},
      sessionId 
    }: ChatRequest = req.body;

    // Validate required fields
    if (!message || !userRole) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: message, userRole'
      });
    }

    console.log(`🤖 Processing AI chat request for ${userRole} in ${language}`);
    console.log(`📝 Message: ${message.substring(0, 100)}...`);

    // Build conversation messages
    const systemMessage = aiService.buildSystemMessage(userRole, language, conversationContext);
    const messages = [
      systemMessage,
      { role: 'user' as const, content: message }
    ];

    // Generate AI response with intelligent fallback
    const startTime = Date.now();
    const aiResponse = await aiService.generateResponse(
      messages,
      userRole,
      language,
      conversationContext,
      {
        temperature: 0.8,
        max_tokens: 800,
        timeout: 12000 // 12 second timeout
      }
    );

    const responseId = `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const currentSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`✅ AI response generated via ${aiResponse.provider} in ${aiResponse.responseTime}ms`);

    // Return successful response
    return res.status(200).json({
      success: true,
      message: 'AI response generated successfully',
      data: {
        aiMessage: {
          id: responseId,
          role: 'assistant',
          content: aiResponse.content,
          timestamp: new Date().toISOString(),
          provider: aiResponse.provider,
          responseTime: aiResponse.responseTime
        },
        sessionId: currentSessionId,
        conversationContext: {
          ...conversationContext,
          lastProvider: aiResponse.provider,
          messageCount: (conversationContext.messageCount || 0) + 1,
          totalResponseTime: (conversationContext.totalResponseTime || 0) + aiResponse.responseTime
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Error in AI chat endpoint:', error);
    
    // Return error response
    return res.status(500).json({
      success: false,
      message: 'Failed to generate AI response',
      error: error.message
    });
  }
}

/**
 * Test endpoint for AI service connectivity
 */
export async function testAIConnectivity(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const results = await aiService.testConnectivity();
    
    return res.status(200).json({
      success: true,
      message: 'AI connectivity test completed',
      data: results
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'AI connectivity test failed',
      error: error.message
    });
  }
}
